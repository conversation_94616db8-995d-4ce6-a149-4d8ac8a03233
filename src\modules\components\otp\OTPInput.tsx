import React, { useState, useRef, useEffect, KeyboardEvent, ClipboardEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Container, Typography } from '@/shared/components/common';

export interface OTPInputProps {
  /**
   * Số lượng ô nhập OTP
   */
  length?: number;

  /**
   * Callback khi OTP được nhập đầy đủ
   */
  onComplete?: (otp: string) => void;

  /**
   * Callback khi nhấn nút xác thực
   */
  onVerify?: (otp: string) => void;

  /**
   * Callback khi nhấn nút đặt lại
   */
  onReset?: () => void;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;

  /**
   * Tiêu đề hiển thị phía trên OTP input
   */
  title?: string;

  /**
   * Kích thước của mỗi ô input
   */
  inputSize?: 'sm' | 'md' | 'lg';

  /**
   * Tự động focus vào ô đầu tiên khi component được render
   */
  autoFocus?: boolean;

  /**
   * Class bổ sung
   * @deprecated Sử dụng containerClassName thay thế
   */
  className?: string;

  /**
   * Class bổ sung cho container
   */
  containerClassName?: string;
}

/**
 * Component OTP Input cho phép người dùng nhập mã OTP
 * Hỗ trợ tự động focus, dán OTP, responsive và đa ngôn ngữ
 */
const OTPInput: React.FC<OTPInputProps> = ({
  length = 6,
  onComplete,
  onVerify,
  onReset,
  isLoading = false,
  title,
  inputSize = 'md',
  autoFocus = true,
  className = '',
  containerClassName = '',
}) => {
  const { t } = useTranslation();

  // State để lưu giá trị của từng ô input
  const [otpValues, setOtpValues] = useState<string[]>(Array(length).fill(''));

  // Refs cho các ô input
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Khởi tạo refs cho các ô input
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Auto focus vào ô đầu tiên khi component được render
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  // Xử lý khi OTP được nhập đầy đủ
  useEffect(() => {
    if (otpValues.every(value => value !== '')) {
      onComplete?.(otpValues.join(''));
    }
  }, [otpValues, onComplete]);

  // Xử lý khi người dùng nhập vào ô input
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;

    // Chỉ lấy ký tự số đầu tiên
    const digit = value.replace(/[^0-9]/g, '').slice(-1);

    // Cập nhật state
    const newOtpValues = [...otpValues];
    newOtpValues[index] = digit;
    setOtpValues(newOtpValues);

    // Nếu có giá trị và không phải ô cuối cùng, focus vào ô tiếp theo
    if (digit && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Xử lý khi người dùng nhấn phím
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    // Nếu nhấn Backspace và ô hiện tại trống, focus vào ô trước đó
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }

    // Nếu nhấn mũi tên trái, focus vào ô trước đó
    if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }

    // Nếu nhấn mũi tên phải, focus vào ô tiếp theo
    if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Xử lý khi người dùng dán OTP
  const handlePaste = (e: ClipboardEvent<HTMLInputElement>, index: number) => {
    e.preventDefault();

    // Lấy text từ clipboard
    const pasteData = e.clipboardData.getData('text');

    // Lọc ra các ký tự số
    const digits = pasteData
      .replace(/[^0-9]/g, '')
      .split('')
      .slice(0, length);

    if (digits.length > 0) {
      // Cập nhật state với các ký tự đã dán
      const newOtpValues = [...otpValues];

      // Điền các ký tự vào các ô, bắt đầu từ ô hiện tại
      for (let i = 0; i < digits.length && index + i < length; i++) {
        const digit = digits[i];
        if (digit !== undefined) {
          newOtpValues[index + i] = digit;
        }
      }

      setOtpValues(newOtpValues);

      // Focus vào ô tiếp theo sau khi dán
      const nextIndex = Math.min(index + digits.length, length - 1);
      inputRefs.current[nextIndex]?.focus();
    }
  };

  // Xử lý khi nhấn nút xác thực
  const handleVerify = () => {
    const otp = otpValues.join('');
    onVerify?.(otp);
  };

  // Xử lý khi nhấn nút đặt lại
  const handleReset = () => {
    setOtpValues(Array(length).fill(''));
    inputRefs.current[0]?.focus();
    onReset?.();
  };

  // Xác định kích thước input dựa trên prop inputSize
  const getInputSizeClasses = () => {
    switch (inputSize) {
      case 'sm':
        return 'w-8 h-8 text-lg';
      case 'lg':
        return 'w-14 h-14 text-2xl';
      case 'md':
      default:
        return 'w-10 h-10 text-xl';
    }
  };

  // Lớp CSS cho input
  const inputClasses = `
    ${getInputSizeClasses()}
    rounded-md
    border
    border-gray-300
    dark:border-gray-600
    bg-white
    dark:bg-gray-800
    text-center
    font-medium
    focus:outline-none
    focus:ring-1
    focus:ring-primary
    focus:border-transparent
    transition-all
    duration-200
  `;

  // Lớp CSS cho input đã nhập
  const inputFilledClasses = `
    ${inputClasses}
    border-primary
    dark:border-primary
  `;

  return (
    <Container className={containerClassName || className}>
      {title && (
        <Typography variant="h6" className="text-center mb-4">
          {title}
        </Typography>
      )}

      <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-6">
        {Array.from({ length }, (_, index) => (
          <input
            key={index}
            ref={el => (inputRefs.current[index] = el)}
            type="text"
            inputMode="numeric"
            autoComplete="one-time-code"
            maxLength={1}
            value={otpValues[index]}
            onChange={e => handleChange(e, index)}
            onKeyDown={e => handleKeyDown(e, index)}
            onPaste={e => handlePaste(e, index)}
            className={otpValues[index] ? inputFilledClasses : inputClasses}
            disabled={isLoading}
            aria-label={`OTP digit ${index + 1}`}
          />
        ))}
      </div>

      <div className="flex flex-wrap gap-4 justify-center">
        <Button
          variant="primary"
          onClick={handleVerify}
          isLoading={isLoading}
          disabled={otpValues.some(value => value === '') || isLoading}
        >
          {t('common.verifyOTP', 'Xác thực')}
        </Button>

        <Button variant="outline" onClick={handleReset} disabled={isLoading}>
          {t('common.reset', 'Đặt lại')}
        </Button>
      </div>
    </Container>
  );
};

export default OTPInput;
