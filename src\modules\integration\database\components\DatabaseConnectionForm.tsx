import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FormItem,
  FormGrid,
  Input,
  Select,
  Toggle,
  Button,
  Icon,
  Typography,
  Card,
} from '@/shared/components/common';
import { DatabaseConnectionConfig, DatabaseConnectionFormData, DatabaseType } from '../types';
import { DATABASE_CONNECTION_TEMPLATES } from '../constants';
import { databaseConnectionFormSchema, validateCredentialsByType } from '../schemas';

interface DatabaseConnectionFormProps {
  initialData?: DatabaseConnectionConfig;
  onSubmit: (data: DatabaseConnectionFormData) => void;
  onCancel: () => void;
  loading?: boolean;
  mode?: 'create' | 'edit' | 'view';
}

/**
 * Database Connection Form Component
 * Dynamic form for creating/editing database connections
 */
const DatabaseConnectionForm: React.FC<DatabaseConnectionFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = 'create',
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [, setFormErrors] = useState<Record<string, string>>({});
  
  const [formData, setFormData] = useState<DatabaseConnectionFormData>({
    name: '',
    type: 'mysql',
    displayName: '',
    description: '',
    credentials: {},
    settings: {},
    isDefault: false,
  });

  const [showPassword, setShowPassword] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  // Initialize form data
  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        type: initialData.type,
        displayName: initialData.displayName,
        description: initialData.description || '',
        credentials: initialData.credentials,
        settings: initialData.settings,
        isDefault: initialData.isDefault,
      });
    }
  }, [initialData]);

  // Get database type options
  const databaseTypeOptions = useMemo(() => {
    return Object.values(DATABASE_CONNECTION_TEMPLATES).map(template => ({
      value: template.type,
      label: template.displayName,
      description: template.description,
    }));
  }, []);

  // Get current template
  const currentTemplate = useMemo(() => {
    return DATABASE_CONNECTION_TEMPLATES[formData.type];
  }, [formData.type]);

  // Handle form field changes
  const handleFieldChange = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle credentials field changes
  const handleCredentialsChange = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      credentials: {
        ...prev.credentials,
        [field]: value as string | number | boolean | undefined,
      },
    }));
  };

  // Handle database type change
  const handleTypeChange = (type: DatabaseType) => {
    const template = DATABASE_CONNECTION_TEMPLATES[type];
    setFormData(prev => ({
      ...prev,
      type,
      credentials: {}, // Reset credentials when type changes
      settings: {
        ...prev.settings,
        ...(template?.defaultSettings || {}),
      },
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (mode === 'view') return;

    try {
      // Validate form data
      const validatedData = databaseConnectionFormSchema.parse(formData);

      // Validate credentials based on database type
      validateCredentialsByType(formData.type, formData.credentials);

      onSubmit(validatedData);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Form validation error:', error);
        // Handle validation errors
        setFormErrors({ name: error.message });
        alert(error.message); // Temporary error display
      }
    }
  };

  // Test connection
  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    try {
      // Validate credentials first
      validateCredentialsByType(formData.type, formData.credentials);
      
      // Here you would call the test connection API
      // For now, simulate a test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Show success message
      alert(t('admin:integration.database.messages.testSuccess'));
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Test failed';
      alert(t('admin:integration.database.messages.testError') + ': ' + errorMessage);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const isReadOnly = mode === 'view';

  return (
    <div className="w-full mx-auto">
      <div className="mb-6">
        <Typography variant="h4" className="mb-2">
          {mode === 'create' && t('admin:integration.database.actions.add')}
          {mode === 'edit' && t('admin:integration.database.actions.edit')}
          {mode === 'view' && t('admin:integration.database.actions.view')}
        </Typography>
        <Typography variant="body1" color="muted">
          {mode === 'create' && t('admin:integration.database.form.createDescription')}
          {mode === 'edit' && t('admin:integration.database.form.editDescription')}
          {mode === 'view' && t('admin:integration.database.form.viewDescription')}
        </Typography>
      </div>

      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6">
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            {t('admin:integration.database.form.basicInfo')}
          </Typography>
          
          <FormGrid columns={2} columnsSm={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:integration.database.form.name')}
              required
              helpText={t('admin:integration.database.form.nameHelp')}
            >
              <Input
                value={formData.name}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                placeholder={t('admin:integration.database.form.namePlaceholder')}
                disabled={isReadOnly}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="displayName"
              label={t('admin:integration.database.form.displayName')}
              required
            >
              <Input
                value={formData.displayName}
                onChange={(e) => handleFieldChange('displayName', e.target.value)}
                placeholder={t('admin:integration.database.form.displayNamePlaceholder')}
                disabled={isReadOnly}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="type"
              label={t('admin:integration.database.form.type')}
              required
              helpText={currentTemplate?.description}
            >
              <Select
                value={formData.type}
                onChange={(value) => handleTypeChange(value as DatabaseType)}
                options={databaseTypeOptions}
                placeholder={t('admin:integration.database.form.selectType')}
                disabled={isReadOnly}
                fullWidth
              />
            </FormItem>

            <FormItem name="isDefault" label={t('admin:integration.database.form.isDefault')} inline>
              <Toggle
                checked={formData.isDefault}
                onChange={(checked) => handleFieldChange('isDefault', checked)}
                disabled={isReadOnly}
              />
            </FormItem>
          </FormGrid>

          <FormItem
            name="description"
            label={t('admin:integration.database.form.description')}
            className="mt-4"
          >
            <Input
              value={formData.description}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              placeholder={t('admin:integration.database.form.descriptionPlaceholder')}
              disabled={isReadOnly}
              fullWidth
            />
          </FormItem>
        </Card>

        {/* Connection Credentials */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            {t('admin:integration.database.form.credentials')}
          </Typography>

          {/* MySQL & PostgreSQL Fields */}
          {(formData.type === 'mysql' || formData.type === 'postgresql') && (
            <FormGrid columns={2} columnsSm={1} gap="md">
              <FormItem
                name="host"
                label={t('admin:integration.database.form.host')}
                required
              >
                <Input
                  value={formData.credentials.host || ''}
                  onChange={(e) => handleCredentialsChange('host', e.target.value)}
                  placeholder="localhost"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="port"
                label={t('admin:integration.database.form.port')}
                required
              >
                <Input
                  type="number"
                  value={formData.credentials.port || currentTemplate?.defaultPort || ''}
                  onChange={(e) => handleCredentialsChange('port', parseInt(e.target.value))}
                  placeholder={String(currentTemplate?.defaultPort)}
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="username"
                label={t('admin:integration.database.form.username')}
                required
              >
                <Input
                  value={formData.credentials.username || ''}
                  onChange={(e) => handleCredentialsChange('username', e.target.value)}
                  placeholder="username"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="password"
                label={t('admin:integration.database.form.password')}
                required
              >
                <Input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.credentials.password || ''}
                  onChange={(e) => handleCredentialsChange('password', e.target.value)}
                  placeholder="••••••••"
                  disabled={isReadOnly}
                  rightIcon={
                    <div onClick={() => setShowPassword(!showPassword)} className="cursor-pointer">
                      <Icon name={showPassword ? 'eye-off' : 'eye'} size="sm" />
                    </div>
                  }
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="database"
                label={t('admin:integration.database.form.database')}
                required
              >
                <Input
                  value={formData.credentials.database || ''}
                  onChange={(e) => handleCredentialsChange('database', e.target.value)}
                  placeholder="database_name"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              {formData.type === 'postgresql' && (
                <FormItem
                  name="schema"
                  label={t('admin:integration.database.form.schema')}
                >
                  <Input
                    value={formData.credentials.schema || ''}
                    onChange={(e) => handleCredentialsChange('schema', e.target.value)}
                    placeholder="public"
                    disabled={isReadOnly}
                    fullWidth
                  />
                </FormItem>
              )}
            </FormGrid>
          )}

          {/* MongoDB Fields */}
          {formData.type === 'mongodb' && (
            <FormGrid columns={1} gap="md">
              <FormItem
                name="connectionString"
                label={t('admin:integration.database.form.connectionString')}
                required
                helpText={t('admin:integration.database.form.connectionStringHelp')}
              >
                <Input
                  value={formData.credentials.connectionString || ''}
                  onChange={(e) => handleCredentialsChange('connectionString', e.target.value)}
                  placeholder="********************************:port"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              <FormGrid columns={2} columnsSm={1} gap="md">
                <FormItem
                  name="database"
                  label={t('admin:integration.database.form.database')}
                  required
                >
                  <Input
                    value={formData.credentials.database || ''}
                    onChange={(e) => handleCredentialsChange('database', e.target.value)}
                    placeholder="database_name"
                    disabled={isReadOnly}
                    fullWidth
                  />
                </FormItem>

                <FormItem
                  name="collection"
                  label={t('admin:integration.database.form.collection')}
                >
                  <Input
                    value={formData.credentials.collection || ''}
                    onChange={(e) => handleCredentialsChange('collection', e.target.value)}
                    placeholder="collection_name"
                    disabled={isReadOnly}
                    fullWidth
                  />
                </FormItem>
              </FormGrid>
            </FormGrid>
          )}

          {/* Redis Fields */}
          {formData.type === 'redis' && (
            <FormGrid columns={2} columnsSm={1} gap="md">
              <FormItem
                name="host"
                label={t('admin:integration.database.form.host')}
                required
              >
                <Input
                  value={formData.credentials.host || ''}
                  onChange={(e) => handleCredentialsChange('host', e.target.value)}
                  placeholder="localhost"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="port"
                label={t('admin:integration.database.form.port')}
                required
              >
                <Input
                  type="number"
                  value={formData.credentials.port || 6379}
                  onChange={(e) => handleCredentialsChange('port', parseInt(e.target.value))}
                  placeholder="6379"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="password"
                label={t('admin:integration.database.form.password')}
              >
                <Input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.credentials.password || ''}
                  onChange={(e) => handleCredentialsChange('password', e.target.value)}
                  placeholder="••••••••"
                  disabled={isReadOnly}
                  rightIcon={
                    <div onClick={() => setShowPassword(!showPassword)} className="cursor-pointer">
                      <Icon name={showPassword ? 'eye-off' : 'eye'} size="sm" />
                    </div>
                  }
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="databaseIndex"
                label={t('admin:integration.database.form.databaseIndex')}
              >
                <Input
                  type="number"
                  value={formData.credentials.databaseIndex || 0}
                  onChange={(e) => handleCredentialsChange('databaseIndex', parseInt(e.target.value))}
                  placeholder="0"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>
            </FormGrid>
          )}

          {/* SQLite Fields */}
          {formData.type === 'sqlite' && (
            <FormGrid columns={2} columnsSm={1} gap="md">
              <FormItem
                name="filePath"
                label={t('admin:integration.database.form.filePath')}
                required
              >
                <Input
                  value={formData.credentials.filePath || ''}
                  onChange={(e) => handleCredentialsChange('filePath', e.target.value)}
                  placeholder="/path/to/database.db"
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="mode"
                label={t('admin:integration.database.form.mode')}
              >
                <Select
                  value={formData.credentials.mode || 'readwrite'}
                  onChange={(value) => handleCredentialsChange('mode', value)}
                  options={[
                    { value: 'readonly', label: t('admin:integration.database.form.readonly') },
                    { value: 'readwrite', label: t('admin:integration.database.form.readwrite') },
                    { value: 'create', label: t('admin:integration.database.form.create') },
                  ]}
                  disabled={isReadOnly}
                  fullWidth
                />
              </FormItem>
            </FormGrid>
          )}
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-end">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel')}
          </Button>

          {!isReadOnly && (
            <>
              <Button
                variant="outline"
                leftIcon={<Icon name="link" size="sm" />}
                onClick={handleTestConnection}
                isLoading={isTestingConnection}
              >
                {t('admin:integration.database.actions.test')}
              </Button>

              <Button
                type="submit"
                variant="primary"
                leftIcon={<Icon name="save" size="sm" />}
                isLoading={loading}
              >
                {mode === 'create' ? t('common:create') : t('common:save')}
              </Button>
            </>
          )}
        </div>
      </form>
    </div>
  );
};

export default DatabaseConnectionForm;
