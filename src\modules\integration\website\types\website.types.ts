/**
 * Website Integration Types
 * Định nghĩa các interface cho Website integration
 */

/**
 * Enum cho các trường sắp xếp Website
 */
export enum WebsiteSortBy {
  WEBSITE_NAME = 'websiteName',
  HOST = 'host',
  CREATED_AT = 'createdAt',
  VERIFY = 'verify',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho Website Response
 */
export interface WebsiteDto {
  id: string;
  host: string;
  verify: boolean;
  agentId?: string | null;
  agentName?: string | null;
  logo?: string | null;
  createdAt: string;
}

/**
 * Interface cho Website Query Parameters
 */
export interface WebsiteQueryDto {
  page?: number;
  limit?: number;
  search?: string | undefined;
  sortBy?: WebsiteSortBy | undefined;
  sortDirection?: SortDirection | undefined;
  verify?: boolean | undefined;
}

/**
 * Interface cho Create Website
 */
export interface CreateWebsiteDto {
  websiteName: string;
  host: string;
  logoMime?: string | undefined;
}

/**
 * Interface cho Create Website Response
 */
export interface CreateWebsiteResponseDto {
  logoUploadUrl?: string;
}

/**
 * Interface cho Update Website
 */
export interface UpdateWebsiteDto {
  websiteName?: string;
  host?: string;
  verify?: boolean;
  agentId?: string;
}

/**
 * Interface cho Delete Website
 */
export interface DeleteWebsiteDto {
  id: string;
}

/**
 * Interface cho Connect Agent to Website
 */
export interface ConnectAgentToWebsiteDto {
  agentId: string;
  websiteId: string;
}

/**
 * Interface cho Disconnect Agent from Website
 */
export interface DisconnectAgentFromWebsiteDto {
  websiteId: string;
}

/**
 * Interface cho Website Verification
 */
export interface VerifyWebsiteDto {
  websiteId: string;
  verificationCode?: string;
}

/**
 * Interface cho Website Verification Response
 */
export interface WebsiteVerificationResponseDto {
  success: boolean;
  message: string;
  verificationCode?: string;
}

/**
 * Interface cho Website Statistics
 */
export interface WebsiteStatsDto {
  totalWebsites: number;
  verifiedWebsites: number;
  connectedWebsites: number;
  activeWebsites: number;
}

/**
 * Interface cho Website Analytics
 */
export interface WebsiteAnalyticsDto {
  websiteId: string;
  totalVisits: number;
  uniqueVisitors: number;
  pageViews: number;
  bounceRate: number;
  averageSessionDuration: number;
  conversionRate: number;
  period: {
    startDate: Date;
    endDate: Date;
  };
}
