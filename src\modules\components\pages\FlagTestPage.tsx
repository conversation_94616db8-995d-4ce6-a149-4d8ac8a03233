import React from 'react';
import { 
  Typography, 
  Card, 
  ResponsiveGrid,
  CountryFlag
} from '@/shared/components/common';
import { ComponentDemo } from '../components';
import { COUNTRIES } from '@/shared/data/countries';

/**
 * Demo page để test SVG flags vs emoji flags
 */
const FlagTestPage: React.FC = () => {
  // Lấy một số quốc gia phổ biến để test
  const testCountries = COUNTRIES.slice(0, 12);

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-8">
        {/* Header */}
        <div>
          <Typography variant="h1" className="mb-2">
            Flag Test Page
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            So sánh hiển thị giữa SVG flags và emoji flags trên các quốc gia khác nhau.
          </Typography>
        </div>

        {/* SVG Flags Test */}
        <ComponentDemo
          title="SVG Flags (Mặc định)"
          description="Flags được tải từ flagcdn.com dưới dạng SVG"
        >
          <ResponsiveGrid maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6 }}>
            {testCountries.map((country) => (
              <Card key={`svg-${country.code}`} className="p-4 text-center">
                <div className="flex flex-col items-center gap-2">
                  <CountryFlag 
                    country={country} 
                    size="lg" 
                    useSvg={true}
                  />
                  <div>
                    <Typography variant="body2" className="font-medium">
                      {country.code}
                    </Typography>
                    <Typography variant="caption" className="text-muted-foreground">
                      {country.name}
                    </Typography>
                  </div>
                </div>
              </Card>
            ))}
          </ResponsiveGrid>
        </ComponentDemo>

        {/* Emoji Flags Test */}
        <ComponentDemo
          title="Emoji Flags (Fallback)"
          description="Flags hiển thị dưới dạng emoji"
        >
          <ResponsiveGrid maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6 }}>
            {testCountries.map((country) => (
              <Card key={`emoji-${country.code}`} className="p-4 text-center">
                <div className="flex flex-col items-center gap-2">
                  <CountryFlag 
                    country={country} 
                    size="lg" 
                    useSvg={false}
                  />
                  <div>
                    <Typography variant="body2" className="font-medium">
                      {country.code}
                    </Typography>
                    <Typography variant="caption" className="text-muted-foreground">
                      {country.name}
                    </Typography>
                  </div>
                </div>
              </Card>
            ))}
          </ResponsiveGrid>
        </ComponentDemo>

        {/* Size Comparison */}
        <ComponentDemo
          title="Kích thước khác nhau"
          description="SVG flags với các kích thước: small, medium, large"
        >
          <Card className="p-6">
            <ResponsiveGrid maxColumns={{ xs: 1, md: 3 }}>
              <div className="text-center">
                <Typography variant="h4" className="mb-4">Small</Typography>
                <div className="flex flex-wrap justify-center gap-2">
                  {testCountries.slice(0, 6).map((country) => (
                    <CountryFlag 
                      key={`sm-${country.code}`}
                      country={country} 
                      size="sm" 
                      useSvg={true}
                    />
                  ))}
                </div>
              </div>
              
              <div className="text-center">
                <Typography variant="h4" className="mb-4">Medium</Typography>
                <div className="flex flex-wrap justify-center gap-2">
                  {testCountries.slice(0, 6).map((country) => (
                    <CountryFlag 
                      key={`md-${country.code}`}
                      country={country} 
                      size="md" 
                      useSvg={true}
                    />
                  ))}
                </div>
              </div>
              
              <div className="text-center">
                <Typography variant="h4" className="mb-4">Large</Typography>
                <div className="flex flex-wrap justify-center gap-2">
                  {testCountries.slice(0, 6).map((country) => (
                    <CountryFlag 
                      key={`lg-${country.code}`}
                      country={country} 
                      size="lg" 
                      useSvg={true}
                    />
                  ))}
                </div>
              </div>
            </ResponsiveGrid>
          </Card>
        </ComponentDemo>

        {/* Fallback Test */}
        <ComponentDemo
          title="Fallback Behavior"
          description="Test fallback khi SVG không tải được"
        >
          <Card className="p-6">
            <div className="space-y-4">
              <div>
                <Typography variant="h4" className="mb-2">
                  Normal SVG Flag
                </Typography>
                {testCountries[0] && (
                  <CountryFlag
                    country={testCountries[0]}
                    size="lg"
                    useSvg={true}
                  />
                )}
              </div>
              
              <div>
                <Typography variant="h4" className="mb-2">
                  Emoji Fallback
                </Typography>
                {testCountries[0] && (
                  <CountryFlag
                    country={testCountries[0]}
                    size="lg"
                    useSvg={false}
                  />
                )}
              </div>
              
              <div>
                <Typography variant="h4" className="mb-2">
                  Text Fallback (No emoji)
                </Typography>
                {testCountries[0] && (
                  <CountryFlag
                    country={{
                      code: testCountries[0].code,
                      name: testCountries[0].name,
                      dialCode: testCountries[0].dialCode,
                      flag: '',
                      svgFlag: testCountries[0].svgFlag
                    }}
                    size="lg"
                    useSvg={false}
                    showFallback={true}
                  />
                )}
              </div>
            </div>
          </Card>
        </ComponentDemo>

        {/* Performance Info */}
        <Card>
          <Typography variant="h3" className="mb-4">
            Hiệu suất & Tương thích
          </Typography>
          <ResponsiveGrid maxColumns={{ xs: 1, md: 2 }}>
            <div>
              <Typography variant="h4" className="mb-2 text-green-600">
                SVG Flags (Khuyến nghị)
              </Typography>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>✅ Hiển thị nhất quán trên mọi thiết bị</li>
                <li>✅ Chất lượng cao, không bị mờ</li>
                <li>✅ Tải nhanh với lazy loading</li>
                <li>✅ Responsive, scale tốt</li>
                <li>✅ Fallback tự động về emoji</li>
              </ul>
            </div>
            
            <div>
              <Typography variant="h4" className="mb-2 text-orange-600">
                Emoji Flags (Fallback)
              </Typography>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>⚠️ Hiển thị khác nhau trên các OS</li>
                <li>⚠️ Một số thiết bị không hỗ trợ</li>
                <li>⚠️ Chất lượng phụ thuộc vào font</li>
                <li>✅ Không cần tải từ server</li>
                <li>✅ Tốc độ hiển thị nhanh</li>
              </ul>
            </div>
          </ResponsiveGrid>
        </Card>
      </div>
    </div>
  );
};

export default FlagTestPage;
