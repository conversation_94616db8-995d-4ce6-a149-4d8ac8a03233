import { z } from 'zod';

/**
 * Schema validation cho Website Integration
 */

/**
 * Schema cho tạo mới Website
 */
export const createWebsiteSchema = z.object({
  websiteName: z
    .string()
    .min(1, 'Tên website không được để trống')
    .min(2, 'Tên website phải có ít nhất 2 ký tự')
    .max(100, 'Tên website không được vượt quá 100 ký tự')
    .trim(),
  host: z
    .string()
    .min(1, 'Host không được để trống')
    .transform((val) => {
      if (!val) return val;

      // Loại bỏ khoảng trắng và chuyển về lowercase
      let cleanVal = val.toLowerCase().trim();

      // Nếu có protocol, loại bỏ nó
      cleanVal = cleanVal.replace(/^https?:\/\//, '');

      // Nếu có www., loại bỏ nó
      cleanVal = cleanVal.replace(/^www\./, '');

      // Loại bỏ trailing slash và path
      const parts = cleanVal.split('/');
      cleanVal = parts[0] || cleanVal;

      return cleanVal;
    })
    .refine(
      (val) => {
        // Validate domain name format
        const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
        return domainRegex.test(val);
      },
      'Host không hợp lệ (ví dụ: example.com hoặc https://www.example.com)'
    ),
});

/**
 * Schema cho cập nhật Website
 */
export const updateWebsiteSchema = z.object({
  websiteName: z
    .string()
    .min(1, 'Tên website không được để trống')
    .min(2, 'Tên website phải có ít nhất 2 ký tự')
    .max(100, 'Tên website không được vượt quá 100 ký tự')
    .trim()
    .optional(),
  host: z
    .string()
    .min(1, 'Host không được để trống')
    .transform((val) => {
      if (!val) return val;

      // Loại bỏ khoảng trắng và chuyển về lowercase
      let cleanVal = val.toLowerCase().trim();

      // Nếu có protocol, loại bỏ nó
      cleanVal = cleanVal.replace(/^https?:\/\//, '');

      // Nếu có www., loại bỏ nó
      cleanVal = cleanVal.replace(/^www\./, '');

      // Loại bỏ trailing slash và path
      const parts = cleanVal.split('/');
      cleanVal = parts[0] || cleanVal;

      return cleanVal;
    })
    .refine(
      (val) => {
        // Validate domain name format
        const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
        return domainRegex.test(val);
      },
      'Host không hợp lệ (ví dụ: example.com hoặc https://www.example.com)'
    )
    .optional(),
  verify: z.boolean().optional(),
});

/**
 * Schema cho kết nối Agent với Website
 */
export const connectAgentToWebsiteSchema = z.object({
  agentId: z.string().min(1, 'Agent ID không được để trống'),
  websiteId: z.string().min(1, 'Website ID không được để trống'),
});

/**
 * Schema cho ngắt kết nối Agent khỏi Website
 */
export const disconnectAgentFromWebsiteSchema = z.object({
  websiteId: z.string().min(1, 'Website ID không được để trống'),
});

/**
 * Schema cho xác thực Website
 */
export const verifyWebsiteSchema = z.object({
  websiteId: z.string().min(1, 'Website ID không được để trống'),
  verificationCode: z.string().min(1, 'Mã xác thực không được để trống'),
});

/**
 * Type definitions từ schemas
 */
export type CreateWebsiteFormData = z.infer<typeof createWebsiteSchema>;
export type UpdateWebsiteFormData = z.infer<typeof updateWebsiteSchema>;
export type ConnectAgentToWebsiteFormData = z.infer<typeof connectAgentToWebsiteSchema>;
export type DisconnectAgentFromWebsiteFormData = z.infer<typeof disconnectAgentFromWebsiteSchema>;
export type VerifyWebsiteFormData = z.infer<typeof verifyWebsiteSchema>;

/**
 * Schema factory functions với i18n support
 */
export const WebsiteSchemaFactories = {
  /**
   * Tạo schema cho create website với i18n
   */
  createWebsite: (t?: (key: string, fallback?: string) => string) => {
    const translate = t || ((key: string, fallback?: string) => fallback || key);

    return z.object({
      websiteName: z
        .string()
        .min(1, translate('integration.website.validation.nameRequired', 'Tên website không được để trống'))
        .min(2, translate('integration.website.validation.nameMinLength', 'Tên website phải có ít nhất 2 ký tự'))
        .max(100, translate('integration.website.validation.nameMaxLength', 'Tên website không được vượt quá 100 ký tự'))
        .trim(),
      host: z
        .string()
        .min(1, translate('integration.website.validation.hostRequired', 'Host không được để trống'))
        .transform((val) => {
          if (!val) return val;

          // Loại bỏ khoảng trắng và chuyển về lowercase
          let cleanVal = val.toLowerCase().trim();

          // Nếu có protocol, loại bỏ nó
          cleanVal = cleanVal.replace(/^https?:\/\//, '');

          // Nếu có www., loại bỏ nó
          cleanVal = cleanVal.replace(/^www\./, '');

          // Loại bỏ trailing slash và path
          const parts = cleanVal.split('/');
          cleanVal = parts[0] || cleanVal;

          return cleanVal;
        })
        .refine(
          (val) => {
            // Validate domain name format
            const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
            return domainRegex.test(val);
          },
          translate('integration.website.validation.hostInvalid', 'Host không hợp lệ (ví dụ: example.com hoặc https://www.example.com)')
        ),
    });
  },

  /**
   * Tạo schema cho update website với i18n
   */
  updateWebsite: (t?: (key: string, fallback?: string) => string) => {
    const translate = t || ((key: string, fallback?: string) => fallback || key);

    return z.object({
      websiteName: z
        .string()
        .min(1, translate('integration.website.validation.nameRequired', 'Tên website không được để trống'))
        .min(2, translate('integration.website.validation.nameMinLength', 'Tên website phải có ít nhất 2 ký tự'))
        .max(100, translate('integration.website.validation.nameMaxLength', 'Tên website không được vượt quá 100 ký tự'))
        .trim()
        .optional(),
      host: z
        .string()
        .min(1, translate('integration.website.validation.hostRequired', 'Host không được để trống'))
        .transform((val) => {
          if (!val) return val;

          // Loại bỏ khoảng trắng và chuyển về lowercase
          let cleanVal = val.toLowerCase().trim();

          // Nếu có protocol, loại bỏ nó
          cleanVal = cleanVal.replace(/^https?:\/\//, '');

          // Nếu có www., loại bỏ nó
          cleanVal = cleanVal.replace(/^www\./, '');

          // Loại bỏ trailing slash và path
          const parts = cleanVal.split('/');
          cleanVal = parts[0] || cleanVal;

          return cleanVal;
        })
        .refine(
          (val) => {
            // Validate domain name format
            const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
            return domainRegex.test(val);
          },
          translate('integration.website.validation.hostInvalid', 'Host không hợp lệ (ví dụ: example.com hoặc https://www.example.com)')
        )
        .optional(),
      verify: z.boolean().optional(),
    });
  },
};
