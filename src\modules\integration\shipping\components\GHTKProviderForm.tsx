import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
  Input,
} from '@/shared/components/common';
import { ShippingProviderConfiguration } from '../types';
import { ghtkConfigSchema } from '../schemas';

interface GHTKProviderFormProps {
  initialData?: ShippingProviderConfiguration | null;
  onSubmit?: (values: Record<string, unknown>) => void | Promise<void>;
  onCancel?: () => void;
  onBack?: () => void;
  isSubmitting?: boolean;
  readOnly?: boolean;
}

interface GHTKFormData {
  name: string;
  token: string;
  timeout: number;
  isTestMode: boolean;
}

// Define the API data structure for GHTK configuration
interface GHTKApiData extends Record<string, unknown> {
  name: string;
  type?: 'GHTK';
  ghtkConfig: {
    token: string;
    timeout: number;
  };
}

/**
 * Form component cho việc cấu hình <PERSON>
 */
const GHTKProviderForm: React.FC<GHTKProviderFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  onBack,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isLoading = isSubmitting;
  const [formData, setFormData] = useState<GHTKFormData>({
    name: '',
    token: '',
    timeout: 30000,
    isTestMode: true,
  });

  const isEditing = !!initialData;

  // Load provider data when editing
  useEffect(() => {
    if (initialData && initialData.type === 'GHTK') {
      console.log('🔍 GHTK Form initialData:', initialData);
      
      const mappedData: GHTKFormData = {
        name: initialData.name || '',
        token: readOnly && initialData.ghtkConfig?.hasToken
          ? '***ENCRYPTED***'
          : '',
        timeout: initialData.ghtkConfig?.timeout || 30000,
        isTestMode: initialData.ghtkConfig?.isTestMode || true,
      };

      console.log('🔍 GHTK Mapped form data:', mappedData);
      setFormData(mappedData);
    }
  }, [initialData, readOnly]);

  // Handle form field changes
  const handleFieldChange = (
    field: keyof GHTKFormData,
    value: string | number | boolean
  ) => {
    setFormData((prev: GHTKFormData) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (readOnly) return;

    try {
      console.log('🔍 GHTK Form data before submission:', formData);

      // Validate using Zod schema
      const validationData = {
        token: formData.token.trim(),
        timeout: formData.timeout,
        isTestMode: formData.isTestMode,
      };

      // Only validate token if not editing or if token is provided
      if (!isEditing || formData.token.trim()) {
        const validatedConfig = ghtkConfigSchema.parse(validationData);
        console.log('🔍 GHTK Validated config:', validatedConfig);
      }

      // Validate name
      if (!formData.name.trim()) {
        setErrors({ name: 'Tên hiển thị là bắt buộc' });
        return;
      }

      // Prepare data for API according to the structure you provided
      const apiData: GHTKApiData = {
        name: formData.name.trim(),
        ghtkConfig: {
          token: formData.token.trim(),
          timeout: formData.timeout,
        },
      };

      // Only include type for create (not edit)
      if (!isEditing) {
        apiData.type = 'GHTK';
      }

      console.log('🔍 GHTK API data to submit:', apiData);

      // Call parent onSubmit handler
      await onSubmit?.(apiData);
      console.log('✅ GHTK Form submitted successfully');

      // Auto close form on success
      if (onCancel) {
        onCancel();
      }
    } catch (error: unknown) {
      console.error('❌ GHTK Form submission error:', error);
      if (error && typeof error === 'object' && 'errors' in error) {
        // Handle Zod validation errors
        const fieldErrors: Record<string, string> = {};
        const zodError = error as { errors: Array<{ path: (string | number)[]; message: string }> };
        zodError.errors.forEach(err => {
          if (err.path && err.path.length > 0) {
            fieldErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(fieldErrors);
        console.log('🔍 GHTK Validation errors:', fieldErrors);
      }
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="w-full p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          {onBack && !readOnly && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="mr-2"
            >
              <Icon name="arrow-left" size="sm" />
            </Button>
          )}
          <Icon name="truck" size="lg" className="text-green-600" />
          <Typography variant="h3">
            {readOnly
              ? 'Xem chi tiết cấu hình GHTK'
              : isEditing
              ? 'Chỉnh sửa cấu hình GHTK'
              : 'Cấu hình GHTK - Giao Hàng Tiết Kiệm'}
          </Typography>
        </div>

        {/* Form */}
        <div className="w-full space-y-6">
          {/* Provider Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Tên hiển thị <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.name}
              onChange={e => handleFieldChange('name', e.target.value)}
              placeholder="Ví dụ: Cấu hình GHTK chính"
              disabled={readOnly}
              fullWidth
              error={errors['name']}
            />
            {errors['name'] && (
              <p className="text-sm text-red-500">{errors['name']}</p>
            )}
          </div>

          {/* Token */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Token <span className="text-red-500">*</span>
            </label>
            <Input
              type={readOnly ? 'text' : 'password'}
              value={formData.token}
              onChange={e => handleFieldChange('token', e.target.value)}
              placeholder={readOnly ? "Dữ liệu đã được mã hóa" : isEditing ? "Nhập Token mới (để trống nếu không thay đổi)" : "Nhập Token từ GHTK"}
              disabled={readOnly}
              fullWidth
              error={errors['token']}
            />
            {errors['token'] && (
              <p className="text-sm text-red-500">{errors['token']}</p>
            )}
          </div>

          {/* Timeout */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Timeout (ms)
            </label>
            <Input
              type="number"
              value={formData.timeout.toString()}
              onChange={e => handleFieldChange('timeout', parseInt(e.target.value) || 30000)}
              placeholder="30000"
              disabled={readOnly}
              fullWidth
            />
          </div>



          {/* Actions */}
          <div className="w-full pt-6 border-t border-border">
            <div className="flex flex-col sm:flex-row gap-3 justify-end">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
                  {readOnly ? t('common:close') : t('common:cancel')}
                </Button>
              )}

              {!readOnly && (
                <Button
                  type="button"
                  variant="primary"
                  isLoading={isLoading}
                  onClick={handleSubmit}
                >
                  {isEditing ? t('common:save') : 'Tạo cấu hình GHTK'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GHTKProviderForm;
